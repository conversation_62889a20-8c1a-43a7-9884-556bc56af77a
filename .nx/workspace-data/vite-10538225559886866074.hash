{"12233736090417676830vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "."}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "bun vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"command": "vite", "options": {"cwd": "."}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "bun vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"command": "vite", "options": {"cwd": "."}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "bun vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "."}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "bun vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.app.json", "options": {"cwd": "."}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "bun tsc -p tsconfig.app.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"dependsOn": ["build-deps"], "command": "bun nx watch --projects tiktok-commerce-link-hub --includeDependentProjects -- bun nx build-deps tiktok-commerce-link-hub"}}, "metadata": {}, "projectType": "application"}, "7773213839727430043apps/frontend/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "apps/frontend"}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "bun vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"command": "vite", "options": {"cwd": "apps/frontend"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "bun vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"command": "vite", "options": {"cwd": "apps/frontend"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "bun vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "apps/frontend"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "bun vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.json", "options": {"cwd": "apps/frontend"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "bun tsc -p tsconfig.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"dependsOn": ["build-deps"], "command": "bun nx watch --projects frontend --includeDependentProjects -- bun nx build-deps frontend"}}, "metadata": {}, "projectType": "application"}}