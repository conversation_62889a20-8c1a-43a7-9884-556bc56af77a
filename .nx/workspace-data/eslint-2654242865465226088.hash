{"2777108958654707276": {"apps/ingestion-api": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/ingestion-api"}, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1351525793997314573": {"apps/product-service": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/product-service"}, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11437884211496987557": {"libs/common": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/common"}, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11794532212689249748": {"libs/config": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/config"}, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8935762120736091864": {"apps/ai-workers/auto-tagger": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/ai-workers/auto-tagger"}, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17148006743420950081": {"apps/ai-workers/caption-parser": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/ai-workers/caption-parser"}, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2608552251272414078": {"apps/ai-workers/thumbnail-generator": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/ai-workers/thumbnail-generator"}, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13040027645621470746": {"apps/frontend": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/frontend"}, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11718362423362347475": {".": {"targets": {"lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{projectRoot}/eslintrc.js", "{workspaceRoot}/eslint.config.js", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "bun eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}}