# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# Environment files
.env
.env.local
.env.development
.env.production
.env.test
*.env.production

# Terraform
*.tfstate
*.tfstate.*
*.tfvars
.terraform/
.terraform.lock.hcl

# Lambda deployment packages (build artifacts)
*.zip

# AI Workers build artifacts
apps/ai-workers/*/dist/
apps/ai-workers/*/node_modules/
apps/ai-workers/*/*.zip

# Database
*.db
*.sqlite

# Temporary files
tmp/
temp/
