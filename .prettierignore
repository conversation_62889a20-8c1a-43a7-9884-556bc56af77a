# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.next/
.nuxt/
out/

# Coverage directory used by tools like istanbul
coverage/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Logs
logs
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Docker
Dockerfile*

# Generated files
*.min.js
*.min.css
*.map

# Package files
*.tgz
*.tar.gz

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# AWS
.aws/

# Temporary files
*.tmp
*.temp
