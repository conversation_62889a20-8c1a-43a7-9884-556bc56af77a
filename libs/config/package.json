{"name": "@tiktok-commerce/config", "version": "1.0.0", "description": "Centralized configuration management for TikTok Commerce Link Hub", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"joi": "^17.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "typescript": "^5.1.3"}, "peerDependencies": {"typescript": "^5.0.0"}}