{"name": "@tiktok-commerce/common", "version": "1.0.0", "description": "Shared common utilities and DTOs for TikTok Commerce Link Hub", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"class-transformer": "^0.5.1", "class-validator": "^0.14.0", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "typescript": "^5.1.3"}, "peerDependencies": {"typescript": "^5.0.0"}}