export const ERROR_CODES = {
  // General errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  INVALID_REQUEST: 'INVALID_REQUEST',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // Validation errors
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_URL: 'INVALID_URL',
  INVALID_TIKTOK_URL: 'INVALID_TIKTOK_URL',
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_PHONE: 'INVALID_PHONE',
  INVALID_UUID: 'INVALID_UUID',
  INVALID_PRICE: 'INVALID_PRICE',
  INVALID_IMAGE_URL: 'INVALID_IMAGE_URL',
  
  // Processing errors
  PROCESSING_FAILED: 'PROCESSING_FAILED',
  PROCESSING_TIMEOUT: 'PROCESSING_TIMEOUT',
  PROCESSING_CANCELLED: 'PROCESSING_CANCELLED',
  INVALID_PROCESSING_TYPE: 'INVALID_PROCESSING_TYPE',
  PROCESSING_QUEUE_FULL: 'PROCESSING_QUEUE_FULL',
  
  // TikTok specific errors
  TIKTOK_VIDEO_NOT_FOUND: 'TIKTOK_VIDEO_NOT_FOUND',
  TIKTOK_VIDEO_PRIVATE: 'TIKTOK_VIDEO_PRIVATE',
  TIKTOK_VIDEO_DELETED: 'TIKTOK_VIDEO_DELETED',
  TIKTOK_API_ERROR: 'TIKTOK_API_ERROR',
  TIKTOK_RATE_LIMITED: 'TIKTOK_RATE_LIMITED',
  
  // Product errors
  PRODUCT_NOT_FOUND: 'PRODUCT_NOT_FOUND',
  PRODUCT_ALREADY_EXISTS: 'PRODUCT_ALREADY_EXISTS',
  PRODUCT_OUT_OF_STOCK: 'PRODUCT_OUT_OF_STOCK',
  INVALID_PRODUCT_DATA: 'INVALID_PRODUCT_DATA',
  
  // Storage errors
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  STORAGE_QUOTA_EXCEEDED: 'STORAGE_QUOTA_EXCEEDED',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  
  // Database errors
  DATABASE_CONNECTION_FAILED: 'DATABASE_CONNECTION_FAILED',
  DATABASE_QUERY_FAILED: 'DATABASE_QUERY_FAILED',
  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
  FOREIGN_KEY_CONSTRAINT: 'FOREIGN_KEY_CONSTRAINT',
  
  // Messaging errors
  MESSAGE_SEND_FAILED: 'MESSAGE_SEND_FAILED',
  QUEUE_NOT_FOUND: 'QUEUE_NOT_FOUND',
  TOPIC_NOT_FOUND: 'TOPIC_NOT_FOUND',
  SUBSCRIPTION_FAILED: 'SUBSCRIPTION_FAILED',
  
  // AI/ML errors
  AI_SERVICE_UNAVAILABLE: 'AI_SERVICE_UNAVAILABLE',
  AI_PROCESSING_FAILED: 'AI_PROCESSING_FAILED',
  INSUFFICIENT_CONFIDENCE: 'INSUFFICIENT_CONFIDENCE',
  MODEL_NOT_LOADED: 'MODEL_NOT_LOADED',
  
  // Authentication errors
  INVALID_TOKEN: 'INVALID_TOKEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_NOT_VERIFIED: 'ACCOUNT_NOT_VERIFIED',
  
  // WhatsApp errors
  WHATSAPP_API_ERROR: 'WHATSAPP_API_ERROR',
  INVALID_PHONE_NUMBER: 'INVALID_PHONE_NUMBER',
  MESSAGE_DELIVERY_FAILED: 'MESSAGE_DELIVERY_FAILED',
  TEMPLATE_NOT_FOUND: 'TEMPLATE_NOT_FOUND'
} as const;

export const ERROR_MESSAGES = {
  [ERROR_CODES.INTERNAL_SERVER_ERROR]: 'An internal server error occurred',
  [ERROR_CODES.INVALID_REQUEST]: 'The request is invalid',
  [ERROR_CODES.UNAUTHORIZED]: 'Authentication required',
  [ERROR_CODES.FORBIDDEN]: 'Access denied',
  [ERROR_CODES.NOT_FOUND]: 'Resource not found',
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'Rate limit exceeded. Please try again later',
  
  [ERROR_CODES.VALIDATION_FAILED]: 'Validation failed',
  [ERROR_CODES.INVALID_URL]: 'Invalid URL format',
  [ERROR_CODES.INVALID_TIKTOK_URL]: 'Invalid TikTok URL format',
  [ERROR_CODES.INVALID_EMAIL]: 'Invalid email format',
  [ERROR_CODES.INVALID_PHONE]: 'Invalid phone number format',
  [ERROR_CODES.INVALID_UUID]: 'Invalid UUID format',
  [ERROR_CODES.INVALID_PRICE]: 'Invalid price format',
  [ERROR_CODES.INVALID_IMAGE_URL]: 'Invalid image URL format',
  
  [ERROR_CODES.PROCESSING_FAILED]: 'Processing failed',
  [ERROR_CODES.PROCESSING_TIMEOUT]: 'Processing timed out',
  [ERROR_CODES.PROCESSING_CANCELLED]: 'Processing was cancelled',
  [ERROR_CODES.INVALID_PROCESSING_TYPE]: 'Invalid processing type',
  [ERROR_CODES.PROCESSING_QUEUE_FULL]: 'Processing queue is full',
  
  [ERROR_CODES.TIKTOK_VIDEO_NOT_FOUND]: 'TikTok video not found',
  [ERROR_CODES.TIKTOK_VIDEO_PRIVATE]: 'TikTok video is private',
  [ERROR_CODES.TIKTOK_VIDEO_DELETED]: 'TikTok video has been deleted',
  [ERROR_CODES.TIKTOK_API_ERROR]: 'TikTok API error',
  [ERROR_CODES.TIKTOK_RATE_LIMITED]: 'TikTok API rate limit exceeded',
  
  [ERROR_CODES.PRODUCT_NOT_FOUND]: 'Product not found',
  [ERROR_CODES.PRODUCT_ALREADY_EXISTS]: 'Product already exists',
  [ERROR_CODES.PRODUCT_OUT_OF_STOCK]: 'Product is out of stock',
  [ERROR_CODES.INVALID_PRODUCT_DATA]: 'Invalid product data',
  
  [ERROR_CODES.FILE_NOT_FOUND]: 'File not found',
  [ERROR_CODES.FILE_TOO_LARGE]: 'File size exceeds limit',
  [ERROR_CODES.INVALID_FILE_TYPE]: 'Invalid file type',
  [ERROR_CODES.STORAGE_QUOTA_EXCEEDED]: 'Storage quota exceeded',
  [ERROR_CODES.UPLOAD_FAILED]: 'File upload failed'
} as const;

export type ErrorCodeType = typeof ERROR_CODES[keyof typeof ERROR_CODES];
