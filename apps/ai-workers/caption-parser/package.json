{"name": "caption-parser-worker", "version": "1.0.0", "description": "AI worker for parsing TikTok captions and extracting product information", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:captions": "ts-node test-caption-parser.ts", "test:captions:env": "cp .env.test .env && npm run test:captions", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"@aws-sdk/client-sns": "^3.450.0", "@aws-sdk/client-sqs": "^3.854.0", "axios": "^1.6.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-node": "^10.9.0", "typescript": "^5.2.0"}, "keywords": ["ai", "nlp", "tiktok", "commerce", "caption-parsing", "aws", "lambda"], "author": "TikTok Commerce Team", "license": "MIT"}