# AWS Configuration
AWS_REGION=us-east-1
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/buylink-dev-caption-parsing
SNS_TOPIC_ARN=arn:aws:sns:us-east-1:123456789012:buylink-dev-new-video-posted

# LLM Configuration (Updated to use OpenRouter)
LLM_PROVIDER=openrouter
LLM_MODEL=microsoft/phi-3.5-mini-128k-instruct
# OPENROUTER_API_KEY will be loaded from AWS Secrets Manager

# Alternative: Local Ollama (for development)
# LLM_PROVIDER=ollama
# LLM_MODEL=phi3:mini
# OLLAMA_BASE_URL=http://localhost:11434

# Legacy: OpenAI (more expensive)
# LLM_PROVIDER=openai
# LLM_MODEL=gpt-3.5-turbo
# OPENAI_API_KEY=your-openai-key-here

# Worker Configuration
BATCH_SIZE=5
MAX_RETRIES=3
VISIBILITY_TIMEOUT=300
WAIT_TIME_SECONDS=20

# Application Configuration
NODE_ENV=development
LOG_LEVEL=info
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_PORT=8080

# Development/Testing
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key
