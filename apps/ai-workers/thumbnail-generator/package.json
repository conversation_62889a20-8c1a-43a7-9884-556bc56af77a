{"name": "thumbnail-generator-worker", "version": "1.0.0", "description": "AI worker for generating product thumbnails from TikTok videos", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:local": "node test-local-thumbnails.js", "test:cli": "node generate-thumbnails-cli.js --file example-urls.txt", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist", "install-deps": "npm install && ./setup-python.sh", "yolo:start": "./venv/bin/python python/yolo_service.py", "setup": "npm install && ./setup-python.sh && npm run build"}, "dependencies": {"@aws-sdk/client-s3": "^3.450.0", "@aws-sdk/client-sns": "^3.450.0", "@aws-sdk/client-sqs": "^3.450.0", "axios": "^1.6.0", "dotenv": "^16.3.1", "sharp": "^0.32.6"}, "devDependencies": {"@types/fluent-ffmpeg": "^2.1.24", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-node": "^10.9.0", "typescript": "^5.2.0"}, "keywords": ["ai", "computer-vision", "tiktok", "commerce", "thumbnail-generation", "yolo", "ffmpeg", "aws", "fargate"], "author": "TikTok Commerce Team", "license": "MIT"}