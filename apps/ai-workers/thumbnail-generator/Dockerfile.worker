# Multi-stage build for Thumbnail Generator Worker
FROM node:18-bullseye AS builder

# Set working directory
WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package*.json ./
COPY requirements.txt ./

# Install Node.js dependencies
RUN npm install --only=production

# Install Python dependencies
RUN pip3 install -r requirements.txt

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-bullseye AS production

# Install system dependencies for production
RUN apt-get update && apt-get install -y \
    ffmpeg \
    python3 \
    python3-pip \
    dumb-init \
    && rm -rf /var/lib/apt/lists/*

# Install yt-dlp via pip
RUN pip3 install yt-dlp

# Create non-root user
RUN groupadd -g 1001 nodejs && \
    useradd -r -u 1001 -g nodejs worker

# Set working directory
WORKDIR /app

# Copy Python requirements and install
COPY requirements.txt ./
RUN pip3 install -r requirements.txt

# Copy built application and dependencies
COPY --from=builder --chown=worker:nodejs /app/dist ./dist
COPY --from=builder --chown=worker:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=worker:nodejs /app/package.json ./package.json
COPY --from=builder --chown=worker:nodejs /app/python ./python

# Create temp directory for video processing
RUN mkdir -p /tmp/thumbnail-generator && chown worker:nodejs /tmp/thumbnail-generator

# Switch to non-root user
USER worker

# Expose health check port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV ENABLE_HEALTH_CHECK=true
ENV LOG_LEVEL=info
ENV PYTHONPATH=/app/python

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:8080/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/index.js"]
