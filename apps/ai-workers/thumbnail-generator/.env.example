# AWS Configuration
AWS_REGION=us-east-1
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/buylink-dev-thumbnail-generation
SNS_TOPIC_ARN=arn:aws:sns:us-east-1:123456789012:buylink-dev-new-video-posted
S3_BUCKET_NAME=buylink-dev-thumbnails

# Video Processing Settings (Updated for TikTok's actual limits)
MAX_VIDEO_SIZE_MB=300
MAX_VIDEO_DURATION_SECONDS=3600
FRAME_EXTRACTION_INTERVAL=2
MAX_FRAMES_TO_ANALYZE=15
THUMBNAILS_TO_GENERATE=5

# YOLO Settings
YOLO_MODEL_PATH=yolov8n.pt
YOLO_CONFIDENCE_THRESHOLD=0.5
YOLO_IOU_THRESHOLD=0.5

# Quality Thresholds
MIN_QUALITY_SCORE=0.4
MIN_BRIGHTNESS_SCORE=0.3
MAX_BLUR_SCORE=0.7

# Output Settings
THUMBNAIL_WIDTH=400
THUMBNAIL_HEIGHT=400
THUMBNAIL_QUALITY=85

# Worker Configuration
BATCH_SIZE=1
MAX_RETRIES=3
VISIBILITY_TIMEOUT=900
WAIT_TIME_SECONDS=20

# Application Configuration
NODE_ENV=development
LOG_LEVEL=info
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_PORT=8080

# Development/Testing
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key
