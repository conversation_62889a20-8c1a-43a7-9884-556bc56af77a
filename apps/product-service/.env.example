# TikTok Commerce Link Hub - Product Service Environment Variables
# Copy this file to .env and fill in the values for local development

# Application Configuration
NODE_ENV=development
PORT=3002
LOG_LEVEL=debug

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://dev.buylink.ug

# DynamoDB Tables (will be populated by Terraform)
DYNAMODB_PRODUCTS_TABLE=buylink-dev-products

# SQS Configuration (will be populated by Terraform)
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/buylink-dev-product-assembly

# SNS Configuration (will be populated by Terraform)
SNS_TOPIC_ARN=arn:aws:sns:us-east-1:123456789012/buylink-dev-new-video-posted

# Queue Processing Configuration
QUEUE_POLL_INTERVAL_MS=5000
MAX_MESSAGES_PER_POLL=10
MESSAGE_VISIBILITY_TIMEOUT_SECONDS=600

# Product Processing Configuration
MAX_PRODUCTS_PER_VIDEO=50
PRODUCT_VALIDATION_TIMEOUT_MS=10000

# Health Check Configuration
HEALTH_CHECK_TIMEOUT_MS=5000
DATABASE_HEALTH_CHECK_TIMEOUT_MS=3000

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Error Handling
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_MS=1000
DLQ_MAX_RECEIVE_COUNT=3