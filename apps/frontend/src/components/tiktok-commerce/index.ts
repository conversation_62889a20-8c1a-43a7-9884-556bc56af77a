
// BuyLink UG Design System Components
export { Button, buttonVariants, type ButtonProps } from "./Button";
export { Input, type InputProps } from "./Input";
export { ProductCard, type ProductCardProps } from "./ProductCard";
export { PageViewCounter, type PageViewCounterProps } from "./PageViewCounter";
export { Layout, type LayoutProps } from "./Layout";
export { Header, type HeaderProps } from "./Header";

// Re-export design tokens for easy access
export * as designTokens from "../../lib/design-tokens";
