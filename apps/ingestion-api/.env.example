# TikTok Commerce Link Hub - Ingestion API Environment Variables
# Copy this file to .env and fill in the values for local development

# Application Configuration
NODE_ENV=development
PORT=3001
LOG_LEVEL=debug

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,https://dev.buylink.ug

# DynamoDB Tables (will be populated by Terraform)
DYNAMODB_USERS_TABLE=buylink-dev-users
DYNAMODB_SHOPS_TABLE=buylink-dev-shops
DYNAMODB_PRODUCTS_TABLE=buylink-dev-products
DYNAMODB_ADMIN_SESSIONS_TABLE=buylink-dev-admin-sessions
DYNAMODB_INGESTION_STATE_TABLE=buylink-dev-ingestion-state

# SNS Topics (will be populated by Terraform)
SNS_NEW_VIDEO_POSTED_TOPIC_ARN=arn:aws:sns:us-east-1:123456789012:buylink-dev-new-video-posted

# Cognito Configuration (will be populated by Terraform)
COGNITO_USER_POOL_ID=us-east-1_XXXXXXXXX
COGNITO_CLIENT_ID=your_cognito_client_id

# Admin Configuration
ADMIN_USERNAME=<EMAIL>

# External API Configuration (stored in AWS Secrets Manager)
# These are retrieved from Secrets Manager at runtime
# APIFY_TOKEN=your_apify_token
# JWT_SECRET=your_jwt_secret
# JWT_SECRET_ADMIN=your_admin_jwt_secret
# ADMIN_PASSWORD_HASH=your_bcrypt_hash

# Apify Configuration
APIFY_ACTOR_ID=clockworks/tiktok-profile-scraper

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_TIMEOUT_MINUTES=30
ADMIN_SESSION_TIMEOUT_MINUTES=60

# Health Check Configuration
HEALTH_CHECK_TIMEOUT_MS=5000

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
