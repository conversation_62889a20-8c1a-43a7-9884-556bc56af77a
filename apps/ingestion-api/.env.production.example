# TikTok Commerce Authentication - Production Environment
# Generated from deployment outputs

# Application Configuration
NODE_ENV=production
PORT=3001
LOG_LEVEL=info

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080,http://127.0.0.1:5173,http://127.0.0.1:3000,https://your-domain.com,https://www.your-domain.com

# AWS Configuration (from deployment outputs)
AWS_REGION=us-east-1
# AWS credentials will be provided by IAM roles in production
# For local testing, you can set these:
# AWS_ACCESS_KEY_ID=your-access-key-id
# AWS_SECRET_ACCESS_KEY=your-secret-access-key

# DynamoDB Configuration (from deployment outputs)
DYNAMODB_USERS_TABLE=tiktok-commerce-users-production
# For local testing with LocalStack, uncomment:
# DYNAMODB_ENDPOINT=http://localhost:4566

# AWS Cognito Configuration (from deployment outputs)
COGNITO_USER_POOL_ID=us-east-1_8HyKxaV2k
COGNITO_CLIENT_ID=t1at2lu5i969rp6cj2n29pcna
COGNITO_CLIENT_SECRET=your-cognito-client-secret
COGNITO_REGION=us-east-1


# Cognito Authentication Configuration
COGNITO_AUTH_FLOW=USER_PASSWORD_AUTH
COGNITO_USERNAME_ATTRIBUTE=preferred_username
COGNITO_PASSWORD_MIN_LENGTH=8
COGNITO_REQUIRE_UPPERCASE=true
COGNITO_REQUIRE_LOWERCASE=true
COGNITO_REQUIRE_NUMBERS=true
COGNITO_REQUIRE_SYMBOLS=false

# JWT Configuration (REQUIRED - Generate a secure secret)
JWT_SECRET=your-jwt-secret-key-here-generate-a-secure-one
JWT_EXPIRES_IN=1h

# Apify Configuration (REQUIRED - Get from your Apify account)
APIFY_TOKEN=your_apify_token_here
APIFY_ACTOR_ID=clockworks/tiktok-profile-scraper
APIFY_TIMEOUT=60

# Security Configuration
BCRYPT_ROUNDS=12
COOKIE_SECURE=true
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=strict

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring Configuration
ENABLE_METRICS=true
ENABLE_TRACING=true
# SENTRY_DSN=your-sentry-dsn-for-error-tracking

# Feature Flags
ENABLE_HANDLE_VALIDATION=true
ENABLE_SUBSCRIPTION_CHECK=true
ENABLE_ANALYTICS=true
ENABLE_CACHING=true

# Development Configuration (for testing)
MOCK_EXTERNAL_APIS=false
SKIP_AUTH_IN_DEV=false
ENABLE_API_DOCS=true
API_DOCS_PATH=/api/docs

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Performance Configuration
REQUEST_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000
BODY_LIMIT=1mb
PARAMETER_LIMIT=1000

# Subscription Configuration
FREE_TRIAL_DAYS=7
SUBSCRIPTION_GRACE_PERIOD_DAYS=3
MAX_SHOP_LINKS_FREE=1
MAX_SHOP_LINKS_PAID=10

# Logging Configuration
LOG_FORMAT=json
LOG_FILE_PATH=./logs/app.log
LOG_MAX_FILE_SIZE=10m
LOG_MAX_FILES=5
