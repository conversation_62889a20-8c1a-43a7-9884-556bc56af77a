# Lambda-specific Dockerfile for Scheduled Ingestion
FROM public.ecr.aws/lambda/nodejs:18

# Copy package files
COPY package*.json ${LAMBDA_TASK_ROOT}/

# Install all dependencies (including dev dependencies for building)
RUN npm install && npm cache clean --force

# Copy source code
COPY . ${LAMBDA_TASK_ROOT}/

# Build the application
RUN npm run build

# Remove dev dependencies after build
RUN npm prune --production

# Set the CMD to the Lambda handler
CMD [ "dist/ingestion/handlers/scheduled-ingestion.handler" ]
