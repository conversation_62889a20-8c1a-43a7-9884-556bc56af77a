# Admin Portal Configuration
# Copy this to your .env file and update the values

# Admin Credentials
ADMIN_USERNAME=<EMAIL>
# Generate this hash using: node -e "const bcrypt = require('bcrypt'); console.log(bcrypt.hashSync('your_password_here', 12));"
ADMIN_PASSWORD_HASH=$2b$12$example.hash.replace.with.actual.bcrypt.hash

# Admin JWT Configuration
JWT_SECRET_ADMIN=your-super-secure-admin-jwt-secret-at-least-32-characters-long
JWT_ACCESS_EXPIRES_IN=900s
JWT_REFRESH_EXPIRES_IN=7d
ADMIN_REFRESH_COOKIE_NAME=admin_refresh

# Admin Sessions Table
ADMIN_SESSIONS_TABLE=AdminSessions

# Example of generating a bcrypt hash:
# node -e "const bcrypt = require('bcrypt'); console.log(bcrypt.hashSync('admin123', 12));"
# This will output something like: $2b$12$abcdef<PERSON>ijklmnopqrstuvwxyz123456789

# Security Notes:
# 1. Use a strong password (minimum 12 characters, mix of letters, numbers, symbols)
# 2. JWT_SECRET_ADMIN should be different from JWT_SECRET
# 3. Store these values securely and never commit them to version control
# 4. Consider using AWS Secrets Manager or similar for production
