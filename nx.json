{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/eslint.config.js"], "cache": true}, "type-check": {"inputs": ["default", "^production"], "cache": true}, "dev": {"cache": false}}, "generators": {"@nx/react": {"application": {"style": "css", "linter": "eslint", "bundler": "vite"}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint"}}, "@nx/nest": {"application": {"linter": "eslint"}}}, "defaultProject": "frontend", "plugins": [{"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "previewTargetName": "preview", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static"}}]}