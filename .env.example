# Environment Configuration
NODE_ENV=development
LOG_LEVEL=info

# API Configuration
API_VERSION=v1
PORT=3000
CORS_ORIGINS=http://localhost:8080,https://localhost:8080

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# DynamoDB Configuration
DYNAMODB_ENDPOINT=http://localhost:4566
DYNAMODB_VIDEOS_TABLE=tiktok-videos-dev
DYNAMODB_PRODUCTS_TABLE=tiktok-products-dev
DYNAMODB_JOBS_TABLE=tiktok-processing-jobs-dev
DYNAMODB_USERS_TABLE=tiktok-users-dev
DYNAMODB_TAGS_TABLE=tiktok-tags-dev
DYNAMODB_ANALYTICS_TABLE=tiktok-analytics-dev

# S3 Configuration
S3_BUCKET=tiktok-commerce-assets-dev
S3_REGION=us-east-1
S3_ENDPOINT=http://localhost:4566

# SNS Configuration
SNS_TOPIC_ARN=arn:aws:sns:us-east-1:000000000000:tiktok-processing-dev
SNS_REGION=us-east-1
SNS_ENDPOINT=http://localhost:4566

# SQS Configuration
SQS_REGION=us-east-1
SQS_ENDPOINT=http://localhost:4566
SQS_CAPTION_ANALYSIS_QUEUE=http://localhost:4566/000000000000/tiktok-caption-analysis-dev
SQS_THUMBNAIL_GENERATION_QUEUE=http://localhost:4566/000000000000/tiktok-thumbnail-generation-dev
SQS_AUTO_TAGGING_QUEUE=http://localhost:4566/000000000000/tiktok-auto-tagging-dev

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TTL=3600

# AI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7
AI_CONFIDENCE_THRESHOLD=0.7
AI_MAX_RETRIES=3
AI_TIMEOUT_MS=30000

# WhatsApp Configuration
WHATSAPP_API_URL=https://graph.facebook.com/v18.0
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id
WEBHOOK_VERIFY_TOKEN=your-webhook-verify-token

# Frontend Configuration
VITE_API_URL=http://localhost:3001/api/v1
VITE_PRODUCT_API_URL=http://localhost:3002/api/v1
VITE_WHATSAPP_API_URL=http://localhost:3003/api/v1

# Monitoring & Analytics
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
FROM_EMAIL=<EMAIL>

# Development Tools
ENABLE_SWAGGER=true
ENABLE_CORS=true
ENABLE_LOGGING=true
ENABLE_METRICS=true

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret
COOKIE_SECRET=your-cookie-secret

# Feature Flags
ENABLE_AI_PROCESSING=true
ENABLE_WHATSAPP_INTEGRATION=true
ENABLE_ANALYTICS=true
ENABLE_CACHING=true

# Performance
MAX_FILE_SIZE=10485760
MAX_REQUEST_SIZE=52428800
REQUEST_TIMEOUT=30000

# Backup & Recovery
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
