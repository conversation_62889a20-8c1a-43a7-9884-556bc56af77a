name: Test Suite

on:
  push:
    branches: [main, develop, staging]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.11'

jobs:
  # Detect changes to determine what to test
  detect-changes:
    name: Detect Changes
    runs-on: ubuntu-latest
    outputs:
      frontend: ${{ steps.changes.outputs.frontend }}
      ingestion-api: ${{ steps.changes.outputs.ingestion-api }}
      product-service: ${{ steps.changes.outputs.product-service }}
      thumbnail-generator: ${{ steps.changes.outputs.thumbnail-generator }}
      caption-parser: ${{ steps.changes.outputs.caption-parser }}
      infrastructure: ${{ steps.changes.outputs.infrastructure }}
      all: ${{ steps.all.outputs.all }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect file changes
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            frontend:
              - 'apps/frontend/**'
            ingestion-api:
              - 'apps/ingestion-api/**'
            product-service:
              - 'apps/product-service/**'
            thumbnail-generator:
              - 'apps/ai-workers/thumbnail-generator/**'
            caption-parser:
              - 'apps/ai-workers/caption-parser/**'
            infrastructure:
              - 'infra/**'

      - name: Check if scheduled run
        id: all
        run: |
          if [[ "${{ github.event_name }}" == "schedule" ]]; then
            echo "all=true" >> $GITHUB_OUTPUT
          else
            echo "all=false" >> $GITHUB_OUTPUT
          fi

  # Lint and test Node.js services
  test-nodejs:
    name: Test Node.js Services
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.all == 'true' || needs.detect-changes.outputs.ingestion-api == 'true' || needs.detect-changes.outputs.product-service == 'true' || needs.detect-changes.outputs.frontend == 'true'
    strategy:
      matrix:
        service: [ingestion-api, product-service, frontend]
        node-version: ['18', '20']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint --workspace=apps/${{ matrix.service }}

      - name: Run type checking
        run: npm run type-check --workspace=apps/${{ matrix.service }}

      - name: Run unit tests
        run: npm run test --workspace=apps/${{ matrix.service }}

      - name: Run integration tests
        if: matrix.service != 'frontend'
        run: npm run test:integration --workspace=apps/${{ matrix.service }} || echo "No integration tests found"

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.service }}-node${{ matrix.node-version }}
          path: |
            apps/${{ matrix.service }}/coverage/
            apps/${{ matrix.service }}/test-results.xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: apps/${{ matrix.service }}/coverage/lcov.info
          flags: ${{ matrix.service }}-node${{ matrix.node-version }}
          name: ${{ matrix.service }}-node${{ matrix.node-version }}

  # Test Python AI workers
  test-python:
    name: Test Python Services
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.all == 'true' || needs.detect-changes.outputs.thumbnail-generator == 'true' || needs.detect-changes.outputs.caption-parser == 'true'
    strategy:
      matrix:
        service: [thumbnail-generator, caption-parser]
        python-version: ['3.10', '3.11', '3.12']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ matrix.service }}-${{ hashFiles('apps/ai-workers/${{ matrix.service }}/requirements.txt') }}

      - name: Install dependencies
        working-directory: apps/ai-workers/${{ matrix.service }}
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-mock pytest-asyncio

      - name: Run linting
        working-directory: apps/ai-workers/${{ matrix.service }}
        run: |
          pip install flake8 black isort
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          black --check .
          isort --check-only .

      - name: Run type checking
        working-directory: apps/ai-workers/${{ matrix.service }}
        run: |
          pip install mypy
          mypy . || echo "Type checking completed with warnings"

      - name: Run unit tests
        working-directory: apps/ai-workers/${{ matrix.service }}
        run: |
          python -m pytest tests/ --cov=. --cov-report=xml --cov-report=html --junitxml=test-results.xml

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.service }}-python${{ matrix.python-version }}
          path: |
            apps/ai-workers/${{ matrix.service }}/htmlcov/
            apps/ai-workers/${{ matrix.service }}/coverage.xml
            apps/ai-workers/${{ matrix.service }}/test-results.xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: apps/ai-workers/${{ matrix.service }}/coverage.xml
          flags: ${{ matrix.service }}-python${{ matrix.python-version }}
          name: ${{ matrix.service }}-python${{ matrix.python-version }}

  # Test infrastructure
  test-infrastructure:
    name: Test Infrastructure
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.all == 'true' || needs.detect-changes.outputs.infrastructure == 'true'
    strategy:
      matrix:
        environment: [dev, staging, prod]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: '1.6.0'

      - name: Terraform Format Check
        working-directory: infra/terraform
        run: terraform fmt -check -recursive

      - name: Terraform Init
        working-directory: infra/terraform
        run: terraform init -backend=false

      - name: Terraform Validate
        working-directory: infra/terraform
        run: terraform validate

      - name: Run Terraform Plan (dry-run)
        working-directory: infra/terraform
        run: |
          terraform plan \
            -var-file="envs/${{ matrix.environment }}/terraform.tfvars" \
            -var="apify_token=dummy_token" \
            -var="openrouter_api_key=dummy_key" \
            -out=tfplan-${{ matrix.environment }}

      - name: Upload Terraform plan
        uses: actions/upload-artifact@v4
        with:
          name: terraform-plan-${{ matrix.environment }}
          path: infra/terraform/tfplan-${{ matrix.environment }}

  # Security testing
  security-test:
    name: Security Testing
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run Semgrep security scan
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten

  # Performance testing
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.all == 'true' || needs.detect-changes.outputs.ingestion-api == 'true' || needs.detect-changes.outputs.product-service == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run performance tests
        run: |
          # Install k6 for load testing
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
          # Run load tests if they exist
          if [ -f "tests/performance/load-test.js" ]; then
            k6 run tests/performance/load-test.js
          else
            echo "No performance tests found"
          fi

  # End-to-end testing
  e2e-test:
    name: End-to-End Testing
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.all == 'true' || needs.detect-changes.outputs.frontend == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright
        run: npx playwright install --with-deps

      - name: Run E2E tests
        run: |
          if [ -f "apps/frontend/e2e/tests" ]; then
            npm run test:e2e --workspace=apps/frontend
          else
            echo "No E2E tests found"
          fi

      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: |
            apps/frontend/test-results/
            apps/frontend/playwright-report/

  # Test summary
  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [test-nodejs, test-python, test-infrastructure, security-test, performance-test, e2e-test]
    if: always()
    steps:
      - name: Create test summary
        run: |
          echo "## 🧪 Test Suite Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Test Type | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Node.js Services | ${{ needs.test-nodejs.result == 'success' && '✅ Passed' || needs.test-nodejs.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Python Services | ${{ needs.test-python.result == 'success' && '✅ Passed' || needs.test-python.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Infrastructure | ${{ needs.test-infrastructure.result == 'success' && '✅ Passed' || needs.test-infrastructure.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security | ${{ needs.security-test.result == 'success' && '✅ Passed' || needs.security-test.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Performance | ${{ needs.performance-test.result == 'success' && '✅ Passed' || needs.performance-test.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| E2E | ${{ needs.e2e-test.result == 'success' && '✅ Passed' || needs.e2e-test.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY

      - name: Check overall status
        run: |
          if [[ "${{ needs.test-nodejs.result }}" == "failure" || "${{ needs.test-python.result }}" == "failure" || "${{ needs.test-infrastructure.result }}" == "failure" || "${{ needs.security-test.result }}" == "failure" ]]; then
            echo "❌ Some tests failed"
            exit 1
          else
            echo "✅ All tests passed or skipped"
          fi
