name: Hotfix Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy hotfix to'
        required: true
        default: 'prod'
        type: choice
        options:
          - staging
          - prod
      service:
        description: 'Service to deploy'
        required: true
        type: choice
        options:
          - ingestion-api
          - product-service
          - thumbnail-generator
          - caption-parser
          - all
      reason:
        description: 'Reason for hotfix deployment'
        required: true
        type: string

env:
  AWS_REGION: us-east-1
  PROJECT_NAME: buylink
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.11'

jobs:
  validate-hotfix:
    name: Validate Hotfix
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate inputs
        run: |
          echo "## 🚨 Hotfix Deployment Request" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment**: ${{ github.event.inputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "**Service**: ${{ github.event.inputs.service }}" >> $GITHUB_STEP_SUMMARY
          echo "**Reason**: ${{ github.event.inputs.reason }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Triggered by**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [[ "${{ github.event.inputs.environment }}" == "prod" ]]; then
            echo "⚠️ **WARNING**: This is a PRODUCTION hotfix deployment!" >> $GITHUB_STEP_SUMMARY
          fi

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  test-service:
    name: Test Service
    runs-on: ubuntu-latest
    if: github.event.inputs.service != 'all'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        if: contains(fromJson('["ingestion-api", "product-service"]'), github.event.inputs.service)
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Python
        if: contains(fromJson('["thumbnail-generator", "caption-parser"]'), github.event.inputs.service)
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Test Node.js service
        if: contains(fromJson('["ingestion-api", "product-service"]'), github.event.inputs.service)
        run: |
          npm ci
          npm run lint --workspace=apps/${{ github.event.inputs.service }}
          npm run test --workspace=apps/${{ github.event.inputs.service }}

      - name: Test Python service
        if: contains(fromJson('["thumbnail-generator", "caption-parser"]'), github.event.inputs.service)
        working-directory: apps/ai-workers/${{ github.event.inputs.service }}
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov
          python -m pytest --cov=. --cov-report=xml

  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: [validate-hotfix, security-scan, test-service]
    if: always() && (needs.test-service.result == 'success' || needs.test-service.result == 'skipped')
    environment: ${{ github.event.inputs.environment }}
    strategy:
      matrix:
        service: ${{ github.event.inputs.service == 'all' && fromJson('["ingestion-api", "product-service", "thumbnail-generator", "caption-parser"]') || fromJson(format('["{0}"]', github.event.inputs.service)) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Determine Dockerfile and context
        id: docker-config
        run: |
          case "${{ matrix.service }}" in
            "ingestion-api")
              echo "dockerfile=Dockerfile" >> $GITHUB_OUTPUT
              echo "context=apps/ingestion-api" >> $GITHUB_OUTPUT
              ;;
            "product-service")
              echo "dockerfile=Dockerfile" >> $GITHUB_OUTPUT
              echo "context=apps/product-service" >> $GITHUB_OUTPUT
              ;;
            "thumbnail-generator")
              echo "dockerfile=Dockerfile.worker" >> $GITHUB_OUTPUT
              echo "context=apps/ai-workers/thumbnail-generator" >> $GITHUB_OUTPUT
              ;;
            "caption-parser")
              echo "dockerfile=Dockerfile" >> $GITHUB_OUTPUT
              echo "context=apps/ai-workers/caption-parser" >> $GITHUB_OUTPUT
              ;;
          esac

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ env.PROJECT_NAME }}-${{ github.event.inputs.environment }}-${{ matrix.service }}
          IMAGE_TAG: hotfix-${{ github.sha }}
        run: |
          # Build and tag image
          docker buildx build \
            --platform linux/amd64 \
            --file ${{ steps.docker-config.outputs.context }}/${{ steps.docker-config.outputs.dockerfile }} \
            --tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG \
            --tag $ECR_REGISTRY/$ECR_REPOSITORY:latest \
            --push \
            ${{ steps.docker-config.outputs.context }}

      - name: Deploy to ECS
        env:
          CLUSTER_NAME: ${{ env.PROJECT_NAME }}-${{ github.event.inputs.environment }}-cluster
          SERVICE_NAME: ${{ env.PROJECT_NAME }}-${{ github.event.inputs.environment }}-${{ matrix.service }}
        run: |
          # Force new deployment
          aws ecs update-service \
            --cluster $CLUSTER_NAME \
            --service $SERVICE_NAME \
            --force-new-deployment \
            --region ${{ env.AWS_REGION }}

          # Wait for deployment to complete
          echo "Waiting for service to stabilize..."
          aws ecs wait services-stable \
            --cluster $CLUSTER_NAME \
            --services $SERVICE_NAME \
            --region ${{ env.AWS_REGION }}

          echo "✅ Hotfix deployed successfully for ${{ matrix.service }}"

  health-check:
    name: Health Check
    runs-on: ubuntu-latest
    needs: build-and-deploy
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Get ALB DNS name
        id: alb
        run: |
          ALB_DNS=$(aws elbv2 describe-load-balancers \
            --names ${{ env.PROJECT_NAME }}-${{ github.event.inputs.environment }}-alb \
            --query 'LoadBalancers[0].DNSName' \
            --output text)
          echo "dns_name=$ALB_DNS" >> $GITHUB_OUTPUT

      - name: Health check services
        run: |
          echo "Running health checks..."
          
          # Only check services that have HTTP endpoints
          if [[ "${{ github.event.inputs.service }}" == "all" || "${{ github.event.inputs.service }}" == "ingestion-api" ]]; then
            echo "Checking Ingestion API..."
            for i in {1..30}; do
              if curl -f -s "http://${{ steps.alb.outputs.dns_name }}/api/ingestion/health"; then
                echo "✅ Ingestion API is healthy"
                break
              fi
              echo "Attempt $i/30 failed, waiting 10 seconds..."
              sleep 10
            done
          fi
          
          if [[ "${{ github.event.inputs.service }}" == "all" || "${{ github.event.inputs.service }}" == "product-service" ]]; then
            echo "Checking Product Service..."
            for i in {1..30}; do
              if curl -f -s "http://${{ steps.alb.outputs.dns_name }}/api/products/health"; then
                echo "✅ Product Service is healthy"
                break
              fi
              echo "Attempt $i/30 failed, waiting 10 seconds..."
              sleep 10
            done
          fi
          
          echo "✅ Health checks completed"

  notify:
    name: Notification
    runs-on: ubuntu-latest
    needs: [build-and-deploy, health-check]
    if: always()
    steps:
      - name: Determine status
        id: status
        run: |
          if [[ "${{ needs.build-and-deploy.result }}" == "success" && "${{ needs.health-check.result }}" == "success" ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "message=✅ Hotfix deployment successful" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "message=❌ Hotfix deployment failed" >> $GITHUB_OUTPUT
          fi

      - name: Create deployment summary
        run: |
          echo "## 🚨 Hotfix Deployment Complete" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Status**: ${{ steps.status.outputs.message }}" >> $GITHUB_STEP_SUMMARY
          echo "**Environment**: ${{ github.event.inputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "**Service**: ${{ github.event.inputs.service }}" >> $GITHUB_STEP_SUMMARY
          echo "**Reason**: ${{ github.event.inputs.reason }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Deployed by**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY

      - name: Notify Slack (if configured)
        if: env.SLACK_WEBHOOK_URL != ''
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ steps.status.outputs.status }}
          channel: '#critical-deployments'
          text: |
            🚨 HOTFIX DEPLOYMENT
            ${{ steps.status.outputs.message }}
            Environment: ${{ github.event.inputs.environment }}
            Service: ${{ github.event.inputs.service }}
            Reason: ${{ github.event.inputs.reason }}
            Deployed by: ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
