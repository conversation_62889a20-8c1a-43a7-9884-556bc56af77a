
@tailwind base;
@tailwind components;
@tailwind utilities;

/* BuyLink UG Design System Tokens */

@layer base {
  :root {
    /* Brand Colors - BuyLink UG Theme */
    --primary: 0 84% 60%; /* #E60023 - High-energy red */
    --primary-foreground: 0 0% 100%;
    
    --secondary: 0 0% 0%; /* Rich black */
    --secondary-foreground: 0 0% 100%;
    
    --accent: 51 100% 50%; /* #FFD700 - Warm gold */
    --accent-foreground: 0 0% 0%;
    
    /* Neutrals */
    --background: 0 0% 100%; /* White */
    --foreground: 0 0% 20%; /* #333333 - Dark grey */
    
    --card: 0 0% 100%;
    --card-foreground: 0 0% 20%;
    
    --muted: 0 0% 96%; /* #F5F5F5 - Light grey */
    --muted-foreground: 0 0% 40%; /* #666666 - Medium grey */
    
    /* Feedback Colors */
    --success: 134 61% 41%; /* #28A745 */
    --success-foreground: 0 0% 100%;
    
    --destructive: 354 70% 54%; /* #DC3545 */
    --destructive-foreground: 0 0% 100%;
    
    --warning: 45 100% 51%; /* #FFC107 */
    --warning-foreground: 0 0% 0%;
    
    /* UI Elements */
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 84% 60%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 20%;
    
    /* Design System Spacing (converted to rem) */
    --space-xs: 0.25rem; /* 4px */
    --space-sm: 0.5rem;  /* 8px */
    --space-md: 1rem;    /* 16px */
    --space-lg: 1.5rem;  /* 24px */
    --space-xl: 2rem;    /* 32px */
    
    /* Typography Scale */
    --font-size-xs: 0.75rem;   /* 12px - Caption */
    --font-size-sm: 0.875rem;  /* 14px - Small */
    --font-size-md: 1rem;      /* 16px - Body */
    --font-size-lg: 1.25rem;   /* 20px - Subhead */
    --font-size-xl: 1.5rem;    /* 24px - Heading */
    --font-size-xxl: 2rem;     /* 32px - Display */
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;
    --radius: var(--radius-md);
    
    /* Elevation/Shadows */
    --elevation-sm: 0 1px 2px rgba(0,0,0,0.05);
    --elevation-md: 0 4px 8px rgba(0,0,0,0.1);
    --elevation-lg: 0 8px 16px rgba(0,0,0,0.15);
  }

  .dark {
    /* Dark mode overrides */
    --background: 0 0% 7%; /* #121212 */
    --foreground: 0 0% 88%; /* #E0E0E0 */
    
    --card: 0 0% 7%;
    --card-foreground: 0 0% 88%;
    
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;
    
    --accent: 45 95% 65%; /* Adjusted gold for dark mode */
    --accent-foreground: 0 0% 0%;
    
    --border: 0 0% 15%;
    --input: 0 0% 15%;
    
    --popover: 0 0% 7%;
    --popover-foreground: 0 0% 88%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    /* Mobile-first base styles */
    font-size: var(--font-size-md);
    line-height: 1.5;
    
    /* Improved tap targets for mobile */
    -webkit-tap-highlight-color: rgba(230, 0, 35, 0.1);
  }
  
  /* Mobile-first heading styles */
  h1 { 
    @apply text-2xl font-bold leading-tight;
    font-size: var(--font-size-xxl);
    line-height: 1.2;
  }
  
  h2 { 
    @apply text-xl font-semibold leading-tight;
    font-size: var(--font-size-xl);
    line-height: 1.2;
  }
  
  h3 { 
    @apply text-lg font-medium;
    font-size: var(--font-size-lg);
  }
  
  /* Ensure minimum tap target size */
  button, 
  [role="button"], 
  input[type="submit"], 
  input[type="button"] {
    min-height: 48px;
    min-width: 48px;
  }
  
  /* Focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
}

/* Component utility classes */
@layer components {
  .btn-block {
    @apply w-full;
  }
  
  .elevation-sm {
    box-shadow: var(--elevation-sm);
  }
  
  .elevation-md {
    box-shadow: var(--elevation-md);
  }
  
  .elevation-lg {
    box-shadow: var(--elevation-lg);
  }
  
  /* Mobile-first container */
  .container-mobile {
    @apply w-full px-4 mx-auto;
    max-width: 100%;
  }
  
  @media (min-width: 640px) {
    .container-mobile {
      @apply px-6;
      max-width: 640px;
    }
  }
  
  @media (min-width: 768px) {
    .container-mobile {
      @apply px-8;
      max-width: 768px;
    }
  }
}
