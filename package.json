{"name": "tiktok-commerce-link-hub", "private": true, "version": "1.0.0", "type": "module", "workspaces": ["apps/*", "libs/*"], "scripts": {"dev": "nx run-many --target=dev --all", "build": "nx run-many --target=build --all", "test": "nx run-many --target=test --all", "lint": "nx run-many --target=lint --all", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "nx run-many --target=type-check --all", "clean": "nx reset && rm -rf dist node_modules apps/*/node_modules libs/*/node_modules", "bootstrap": "npm install && nx run-many --target=install --all", "dev:frontend": "nx dev frontend", "dev:ingestion": "nx dev ingestion-api", "dev:product": "nx dev product-service", "dev:whatsapp": "nx dev whatsapp-service", "build:frontend": "nx build frontend", "build:services": "nx run-many --target=build --projects=ingestion-api,product-service,whatsapp-service", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "infra:plan": "cd infra/terraform && terraform plan -var-file=dev.tfvars", "infra:apply": "cd infra/terraform && terraform apply -var-file=dev.tfvars", "infra:destroy": "cd infra/terraform && terraform destroy -var-file=dev.tfvars", "prepare": "husky install || true"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.1.4", "@nx/eslint": "^20.8.2", "@nx/eslint-plugin": "^20.8.2", "@nx/js": "^20.8.2", "@nx/nest": "^20.8.2", "@nx/next": "^20.8.2", "@nx/node": "^20.8.2", "@nx/react": "^20.8.2", "@nx/vite": "^20.8.2", "@nx/workspace": "^20.8.2", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-config-prettier": "^10.1.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "lovable-tagger": "^1.1.7", "nx": "^20.8.2", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,md}": ["prettier --write"]}}