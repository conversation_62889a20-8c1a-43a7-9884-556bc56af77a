#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# Skip lint-staged (ESLint disabled)
# npx lint-staged

# Skip TypeScript compilation check
# echo "🔧 Checking TypeScript compilation..."
# npm run type-check

# Skip tests for now
# echo "🧪 Running tests..."
# npm run test -- --passWithNoTests --findRelatedTests --bail

echo "✅ Pre-commit checks skipped - ESLint disabled!"
