#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Validating commit message..."

# Check commit message format
# Expected format: type(scope): description
# Examples:
# feat(frontend): add new product catalog component
# fix(api): resolve authentication issue
# docs(readme): update installation instructions

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ Invalid commit message format!"
    echo ""
    echo "Expected format: type(scope): description"
    echo ""
    echo "Types:"
    echo "  feat:     A new feature"
    echo "  fix:      A bug fix"
    echo "  docs:     Documentation only changes"
    echo "  style:    Changes that do not affect the meaning of the code"
    echo "  refactor: A code change that neither fixes a bug nor adds a feature"
    echo "  test:     Adding missing tests or correcting existing tests"
    echo "  chore:    Changes to the build process or auxiliary tools"
    echo "  perf:     A code change that improves performance"
    echo "  ci:       Changes to CI configuration files and scripts"
    echo "  build:    Changes that affect the build system or external dependencies"
    echo ""
    echo "Examples:"
    echo "  feat(frontend): add new product catalog component"
    echo "  fix(api): resolve authentication issue"
    echo "  docs(readme): update installation instructions"
    echo ""
    exit 1
fi

echo "✅ Commit message format is valid!"
